# معرض الصور - AL-SALAMAT

## نظرة عامة
تم إضافة معرض صور متطور وتفاعلي إلى موقع AL-SALAMAT لعرض خدمات وأعمال الشركة بطريقة جذابة ومنظمة.

## الميزات الرئيسية

### 🖼️ عرض الصور
- **عرض شبكي متجاوب**: يتكيف مع جميع أحجام الشاشات
- **عرض قائمة**: إمكانية التبديل بين العرض الشبكي والقائمة
- **صور عالية الجودة**: دعم لجميع صيغ الصور الشائعة
- **تحميل تدريجي**: تحسين الأداء مع التحميل التدريجي للصور

### 🏷️ تصنيف الصور
- **فئات متعددة**:
  - جميع الصور
  - خدماتنا
  - التركيب
  - الإصلاح
  - منتجاتنا

### 📊 إحصائيات المعرض
- **عداد الصور الإجمالي**: عرض العدد الكلي للصور
- **عداد الفئة المحددة**: عرض عدد صور الفئة المختارة
- **تحديث فوري**: تحديث الإحصائيات عند التصفية

### 🔍 النافذة المنبثقة
- **عرض بحجم كامل**: مشاهدة الصور بدقة عالية
- **التنقل بين الصور**: أزرار التنقل السابق/التالي
- **معلومات الصورة**: عرض العنوان والوصف
- **عداد الصور**: موضع الصورة الحالية من المجموعة
- **إغلاق سهل**: إغلاق بالنقر خارج الصورة أو زر الإغلاق

## التحكم بلوحة المفاتيح

### في المعرض الرئيسي
- `←` / `→`: التنقل بين الفئات
- `V`: التبديل بين العرض الشبكي والقائمة

### في النافذة المنبثقة
- `Escape`: إغلاق النافذة
- `←` / `→`: التنقل بين الصور
- `Space`: الانتقال للصورة التالية

## دعم اللمس (الأجهزة المحمولة)
- **السحب لليسار**: الانتقال للصورة التالية
- **السحب لليمين**: الانتقال للصورة السابقة
- **النقر**: فتح/إغلاق النافذة المنبثقة

## التصميم المتجاوب

### الشاشات الكبيرة (Desktop)
- قائمة جانبية ثابتة على اليمين
- عرض شبكي بـ 3-4 أعمدة
- تأثيرات حركة متقدمة

### الأجهزة اللوحية (Tablet)
- قائمة جانبية أسفل المحتوى
- عرض شبكي بـ 2-3 أعمدة
- أزرار أكبر للتحكم

### الهواتف المحمولة (Mobile)
- قائمة فئات أفقية
- عرض عمود واحد
- تحسين للمس والسحب

## الملفات المضافة

### HTML
- إضافة قسم معرض الصور في `index.html`
- نافذة منبثقة للصور
- قائمة جانبية للفئات

### CSS
- تصميم المعرض في `styles.css`
- تأثيرات الحركة والانتقالات
- التصميم المتجاوب

### JavaScript
- `gallery-manager.js`: إدارة المعرض والتصفية
- `image-modal.js`: إدارة النافذة المنبثقة
- دعم لوحة المفاتيح واللمس

## التكامل مع Firebase
المعرض جاهز للتكامل مع Firebase لإدارة الصور ديناميكياً:

```javascript
// إضافة صورة جديدة
galleryManager.addImage({
    src: 'url-to-image',
    title: 'عنوان الصورة',
    description: 'وصف الصورة',
    category: 'services'
});

// حذف صورة
galleryManager.removeImage('image-id');
```

## الأداء والتحسين
- **تحميل تدريجي**: الصور تحمل عند الحاجة
- **ضغط الصور**: تحسين أحجام الملفات
- **ذاكرة التخزين المؤقت**: تخزين الصور المحملة
- **تحميل مسبق**: تحميل الصور المجاورة في النافذة المنبثقة

## إمكانية الوصول (Accessibility)
- **نص بديل**: وصف لجميع الصور
- **دعم لوحة المفاتيح**: تنقل كامل بالمفاتيح
- **تباين عالي**: ألوان واضحة ومقروءة
- **أحجام نص مناسبة**: قابلية قراءة على جميع الأجهزة

## المتطلبات التقنية
- متصفح حديث يدعم ES6+
- دعم CSS Grid و Flexbox
- JavaScript مفعل

## التحديثات المستقبلية
- [ ] دعم الفيديو
- [ ] مشاركة الصور على وسائل التواصل
- [ ] تحميل الصور بالسحب والإفلات
- [ ] معاينة مصغرة للصور
- [ ] بحث في الصور
- [ ] علامات (Tags) للصور

## الدعم والصيانة
للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2024  
**الإصدار**: 1.0.0
