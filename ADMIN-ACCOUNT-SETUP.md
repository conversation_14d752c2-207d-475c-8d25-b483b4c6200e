# إعداد حساب المدير - AL-SALAMAT

## 🔐 إنشاء حساب المدير يدوياً

بعد إصلاح مشكلة إنشاء الحسابات التلقائية، أصبح من الضروري إنشاء حساب المدير يدوياً لضمان الأمان.

## 📋 الخطوات المطلوبة

### 1. الذهاب إلى صفحة التسجيل
1. افتح الموقع في المتصفح
2. اذهب إلى `login.html`
3. اضغط على "إنشاء حساب جديد"

### 2. إدخال بيانات المدير
استخدم البيانات التالية **بالضبط**:

- **البريد الإلكتروني:** `<EMAIL>`
- **الاسم:** `مدير النظام`
- **رقم الهاتف:** `+966501234567` (أو أي رقم صحيح)
- **كلمة المرور:** `admin123456` (أو كلمة مرور قوية من اختيارك)

**⚠️ مهم جداً:** يجب استخدام البريد الإلكتروني `<EMAIL>` بالضبط لأن النظام يتعرف على المدراء من خلال هذا البريد.

### 3. التحقق من إنشاء الحساب
بعد إنشاء الحساب:
1. ستتم إعادة توجيهك للصفحة الرئيسية
2. اذهب إلى `admin.html`
3. يجب أن تتمكن من الدخول تلقائياً

## 🔧 إعدادات Firebase المطلوبة

### تأكد من إعدادات Authentication:
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروع `al-salamat`
3. **Authentication** > **Sign-in method**
4. تأكد من تفعيل:
   - ✅ **Email/Password**
   - ✅ **Anonymous** (للزوار)

### تحقق من قواعد قاعدة البيانات:
```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null",
    
    "users": {
      ".read": "auth != null",
      ".write": "auth != null"
    }
  }
}
```

## 🚨 استكشاف الأخطاء

### إذا لم يتم التعرف على المدير:
1. **تحقق من البريد الإلكتروني:** يجب أن يكون `<EMAIL>` بالضبط
2. **تحقق من Firebase Console:** تأكد من وجود المستخدم في قائمة Authentication
3. **تحقق من قاعدة البيانات:** تأكد من وجود بيانات المستخدم في `users/`

### إذا ظهرت رسالة "حساب المدير غير موجود":
1. تأكد من إنشاء الحساب بالبريد الصحيح
2. تحقق من Console المتصفح للأخطاء
3. جرب تسجيل الدخول من `login.html` أولاً

### إذا لم تظهر صلاحيات المدير:
1. تحقق من بيانات المستخدم في Firebase Database
2. تأكد من وجود `role: "admin"` في بيانات المستخدم
3. تأكد من وجود `permissions` مع جميع الصلاحيات

## 🔒 تحسين الأمان

### بعد إنشاء حساب المدير:
1. **غيّر كلمة المرور الافتراضية** إلى كلمة مرور قوية
2. **فعّل المصادقة الثنائية** إذا كانت متاحة
3. **لا تشارك بيانات المدير** مع أي شخص آخر

### كلمة مرور قوية يجب أن تحتوي على:
- 8 أحرف على الأقل
- أحرف كبيرة وصغيرة
- أرقام
- رموز خاصة (!@#$%^&*)

## 📞 الدعم

إذا واجهت مشاكل في إنشاء حساب المدير:

1. **تحقق من Console المتصفح** (F12) للأخطاء
2. **تأكد من اتصال الإنترنت**
3. **تحقق من إعدادات Firebase**
4. **جرب في متصفح مختلف** أو وضع التصفح الخاص

## ✅ التحقق من نجاح الإعداد

بعد إنشاء حساب المدير بنجاح:
- ✅ يمكن الدخول إلى `admin.html` بدون مشاكل
- ✅ تظهر جميع أقسام لوحة التحكم
- ✅ يمكن إضافة وتعديل المحتوى
- ✅ تظهر رسالة "مرحباً مدير النظام" في لوحة التحكم

## 🔄 إعادة تعيين كلمة المرور

إذا نسيت كلمة مرور المدير:
1. اذهب إلى `login.html`
2. اضغط على "نسيت كلمة المرور؟"
3. أدخل `<EMAIL>`
4. تحقق من بريدك الإلكتروني لرابط إعادة التعيين

---

**ملاحظة:** هذا الإجراء ضروري لضمان أمان النظام ومنع إنشاء حسابات غير مرغوب فيها تلقائياً.
