<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مؤشرات التحميل - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 اختبار مؤشرات التحميل</h1>
        <p>هذا الاختبار يتحقق من ظهور مؤشرات التحميل في البداية ثم اختفائها عند تحميل البيانات.</p>
        
        <div id="test-results">
            <div class="test-result info">
                <strong>📋 حالة الاختبار:</strong> جاري التحضير...
            </div>
        </div>
        
        <div>
            <button class="test-button" onclick="testLoadingIndicators()">🔄 اختبار مؤشرات التحميل</button>
            <button class="test-button" onclick="loadHomepage()">📄 تحميل الصفحة</button>
            <button class="test-button" onclick="checkAfterDelay()">⏰ فحص بعد التحميل</button>
            <button class="test-button" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>
        
        <iframe id="homepage-frame" style="display: none;"></iframe>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = `
                <div class="test-result info">
                    <strong>📋 حالة الاختبار:</strong> تم مسح النتائج
                </div>
            `;
        }

        function testLoadingIndicators() {
            clearResults();
            addResult('🔄 بدء اختبار مؤشرات التحميل...', 'info');
            
            // Load homepage first
            loadHomepage();
            
            // Check immediately after loading
            setTimeout(() => {
                addResult('🔍 فحص مؤشرات التحميل الأولية...', 'info');
                checkInitialLoadingState();
            }, 1000);
            
            // Check after data should be loaded
            setTimeout(() => {
                addResult('🔍 فحص حالة ما بعد التحميل...', 'info');
                checkAfterLoadingState();
            }, 5000);
        }

        function loadHomepage() {
            const iframe = document.getElementById('homepage-frame');
            iframe.style.display = 'block';
            iframe.src = 'index.html?' + Date.now(); // Add timestamp to force reload
            
            iframe.onload = function() {
                addResult('✅ تم تحميل الصفحة الرئيسية', 'success');
            };
            
            iframe.onerror = function() {
                addResult('❌ فشل في تحميل الصفحة الرئيسية', 'error');
            };
        }

        function checkInitialLoadingState() {
            const iframe = document.getElementById('homepage-frame');
            if (!iframe.src) {
                addResult('⚠️ يجب تحميل الصفحة أولاً', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Check about loading spinner
                const aboutLoading = iframeDoc.getElementById('about-loading');
                if (aboutLoading && aboutLoading.style.display !== 'none') {
                    addResult('✅ مؤشر التحميل ظاهر في قسم "من نحن"', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل غير ظاهر في قسم "من نحن"', 'error');
                }
                
                // Check branches loading spinner
                const branchesLoading = iframeDoc.getElementById('branches-loading');
                if (branchesLoading && branchesLoading.style.display !== 'none') {
                    addResult('✅ مؤشر التحميل ظاهر في قسم الفروع', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل غير ظاهر في قسم الفروع', 'error');
                }
                
                // Check contact loading spinners
                const addressLoading = iframeDoc.getElementById('address-loading');
                const hoursLoading = iframeDoc.getElementById('hours-loading');
                
                if (addressLoading && addressLoading.style.display !== 'none') {
                    addResult('✅ مؤشر التحميل ظاهر للعنوان', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل غير ظاهر للعنوان', 'error');
                }
                
                if (hoursLoading && hoursLoading.style.display !== 'none') {
                    addResult('✅ مؤشر التحميل ظاهر لساعات العمل', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل غير ظاهر لساعات العمل', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص مؤشرات التحميل: ${error.message}`, 'error');
            }
        }

        function checkAfterLoadingState() {
            const iframe = document.getElementById('homepage-frame');
            if (!iframe.src) {
                addResult('⚠️ يجب تحميل الصفحة أولاً', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Check if content is loaded and loading spinners are hidden
                const aboutLoading = iframeDoc.getElementById('about-loading');
                const aboutDesc = iframeDoc.getElementById('about-description');
                
                if (aboutLoading && aboutLoading.style.display === 'none') {
                    addResult('✅ تم إخفاء مؤشر التحميل في قسم "من نحن"', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل ما زال ظاهراً في قسم "من نحن"', 'error');
                }
                
                if (aboutDesc && !aboutDesc.classList.contains('hidden') && aboutDesc.textContent.trim() !== '') {
                    addResult('✅ تم عرض محتوى قسم "من نحن"', 'success');
                } else {
                    addResult('⚠️ لم يتم عرض محتوى قسم "من نحن"', 'error');
                }
                
                // Check branches
                const branchesLoading = iframeDoc.getElementById('branches-loading');
                const branchCards = iframeDoc.querySelectorAll('.branch-card');
                
                if (branchesLoading && branchesLoading.style.display === 'none') {
                    addResult('✅ تم إخفاء مؤشر التحميل في قسم الفروع', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل ما زال ظاهراً في قسم الفروع', 'error');
                }
                
                if (branchCards.length > 0) {
                    addResult(`✅ تم عرض ${branchCards.length} فرع`, 'success');
                } else {
                    addResult('⚠️ لم يتم عرض أي فروع', 'error');
                }
                
                // Check contact info
                const addressLoading = iframeDoc.getElementById('address-loading');
                const hoursLoading = iframeDoc.getElementById('hours-loading');
                const addressDisplay = iframeDoc.getElementById('contact-address-display');
                const hoursDisplay = iframeDoc.getElementById('contact-hours-display');
                
                if (addressLoading && addressLoading.style.display === 'none') {
                    addResult('✅ تم إخفاء مؤشر التحميل للعنوان', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل ما زال ظاهراً للعنوان', 'error');
                }
                
                if (hoursLoading && hoursLoading.style.display === 'none') {
                    addResult('✅ تم إخفاء مؤشر التحميل لساعات العمل', 'success');
                } else {
                    addResult('⚠️ مؤشر التحميل ما زال ظاهراً لساعات العمل', 'error');
                }
                
                if (addressDisplay && addressDisplay.textContent.trim() !== '' && !addressDisplay.textContent.includes('جاري التحميل')) {
                    addResult('✅ تم عرض العنوان', 'success');
                } else {
                    addResult('⚠️ لم يتم عرض العنوان', 'error');
                }
                
                if (hoursDisplay && hoursDisplay.textContent.trim() !== '' && !hoursDisplay.textContent.includes('جاري التحميل')) {
                    addResult('✅ تم عرض ساعات العمل', 'success');
                } else {
                    addResult('⚠️ لم يتم عرض ساعات العمل', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في فحص حالة ما بعد التحميل: ${error.message}`, 'error');
            }
        }

        function checkAfterDelay() {
            setTimeout(() => {
                checkAfterLoadingState();
            }, 1000);
        }

        // Auto-run message when page loads
        window.onload = function() {
            addResult('🎯 مرحباً! اضغط على "اختبار مؤشرات التحميل" لبدء الفحص', 'info');
        };
    </script>
</body>
</html>
