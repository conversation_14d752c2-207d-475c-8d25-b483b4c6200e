<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البيانات الافتراضية - AL-SALAMAT</title>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        .secondary {
            background: #6c757d;
        }
        .secondary:hover {
            background: #5a6268;
        }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .section-preview {
            background: #f8f9ff;
            border: 1px solid #667eea;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .section-preview h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        .data-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }
        .data-item strong {
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار نظام البيانات الافتراضية</h1>
        
        <div class="test-section">
            <h2>🔧 تهيئة النظام</h2>
            <button onclick="initializeSystem()">تهيئة Firebase</button>
            <div id="init-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 معاينة البيانات الافتراضية</h2>
            <button onclick="previewDefaultData()">معاينة البيانات الافتراضية</button>
            <div id="preview-results"></div>
        </div>

        <div class="test-section">
            <h2>📤 تصدير البيانات الحالية</h2>
            <button onclick="exportCurrentData()">تصدير البيانات الحالية</button>
            <button onclick="downloadCurrentData()" class="secondary">تحميل البيانات كملف</button>
            <div id="export-results"></div>
        </div>

        <div class="test-section">
            <h2>🔄 إعادة تعيين البيانات</h2>
            <button onclick="resetToDefaultData()" class="danger">إعادة تعيين البيانات الافتراضية</button>
            <div id="reset-results"></div>
        </div>

        <div class="test-section">
            <h2>🔍 فحص البيانات الحالية</h2>
            <button onclick="checkCurrentData()">فحص البيانات الحالية</button>
            <div id="check-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 مقارنة البيانات</h2>
            <button onclick="compareWithDefault()">مقارنة البيانات الحالية مع الافتراضية</button>
            <div id="compare-results"></div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        let database;

        // Default data structure (same as in admin.js)
        const DEFAULT_WEBSITE_DATA = {
            companyInfo: {
                title: "AL-SALAMAT",
                subtitle: "شركة السلامات للخدمات المتكاملة",
                description: "نحن شركة رائدة في تقديم الخدمات المتكاملة والحلول المبتكرة. نفتخر بخبرتنا الواسعة وجودة خدماتنا العالية، ونلتزم بتقديم أفضل الحلول لعملائنا الكرام بأحدث التقنيات والمعدات المتطورة.",
                updatedAt: new Date().toISOString(),
                version: "1.0"
            },
            aboutSection: {
                title: "من نحن",
                description: "شركة السلامات هي شركة رائدة في مجال الخدمات المتكاملة، تأسست بهدف تقديم أفضل الحلول والخدمات لعملائنا الكرام. نحن نفتخر بفريق عمل متخصص وذو خبرة عالية في جميع المجالات التي نعمل بها.\n\nنلتزم بتقديم خدمات عالية الجودة تلبي احتياجات عملائنا وتتجاوز توقعاتهم. نستخدم أحدث التقنيات والمعدات المتطورة لضمان تقديم أفضل النتائج.\n\nرؤيتنا هي أن نكون الخيار الأول لعملائنا في جميع الخدمات التي نقدمها، ونسعى دائماً للتطوير والتحسين المستمر لخدماتنا.",
                updatedAt: new Date().toISOString(),
                version: "1.0"
            },
            contactSection: {
                title: "اتصل بنا",
                infoTitle: "معلومات التواصل",
                address: "######",
                hours: "######",
                updatedAt: new Date().toISOString(),
                version: "1.0"
            },
            siteSettings: {
                maintenance: false,
                contactEmail: "######",
                contactPhone: "######",
                updatedAt: new Date().toISOString(),
                version: "1.0"
            }
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        async function initializeSystem() {
            try {
                addResult('init-results', 'بدء تهيئة Firebase...', 'info');
                
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                database = firebase.database();
                
                // Test connection
                const testRef = database.ref('.info/connected');
                testRef.on('value', (snapshot) => {
                    if (snapshot.val() === true) {
                        addResult('init-results', '✅ تم الاتصال بـ Firebase بنجاح', 'success');
                    } else {
                        addResult('init-results', '❌ فشل الاتصال بـ Firebase', 'error');
                    }
                });
                
            } catch (error) {
                addResult('init-results', `❌ خطأ في التهيئة: ${error.message}`, 'error');
            }
        }

        function previewDefaultData() {
            addResult('preview-results', 'عرض البيانات الافتراضية...', 'info');
            
            let previewHTML = '<div class="section-preview">';
            previewHTML += '<h4>📋 البيانات الافتراضية</h4>';
            
            Object.entries(DEFAULT_WEBSITE_DATA).forEach(([sectionName, sectionData]) => {
                previewHTML += `<div class="data-item">`;
                previewHTML += `<strong>${getSectionDisplayName(sectionName)}:</strong><br>`;
                
                Object.entries(sectionData).forEach(([key, value]) => {
                    if (key !== 'updatedAt' && key !== 'version') {
                        const displayValue = typeof value === 'string' && value.length > 100 
                            ? value.substring(0, 100) + '...' 
                            : value;
                        previewHTML += `&nbsp;&nbsp;• ${getFieldDisplayName(key)}: ${displayValue}<br>`;
                    }
                });
                
                previewHTML += `</div>`;
            });
            
            previewHTML += '</div>';
            
            document.getElementById('preview-results').innerHTML = previewHTML;
        }

        async function exportCurrentData() {
            try {
                addResult('export-results', 'تصدير البيانات الحالية...', 'info');
                
                const sections = ['companyInfo', 'aboutSection', 'contactSection', 'siteSettings'];
                const exportData = {};
                
                for (const section of sections) {
                    const snapshot = await database.ref(section).once('value');
                    exportData[section] = snapshot.val();
                }
                
                exportData._metadata = {
                    exportedAt: new Date().toISOString(),
                    version: "1.0",
                    source: "AL-SALAMAT Test Page"
                };
                
                const dataDisplay = `<div class="data-display">${JSON.stringify(exportData, null, 2)}</div>`;
                document.getElementById('export-results').innerHTML = dataDisplay;
                
                window.exportedData = exportData;
                addResult('export-results', '✅ تم تصدير البيانات بنجاح', 'success');
                
            } catch (error) {
                addResult('export-results', `❌ خطأ في التصدير: ${error.message}`, 'error');
            }
        }

        function downloadCurrentData() {
            if (window.exportedData) {
                const dataText = JSON.stringify(window.exportedData, null, 2);
                const blob = new Blob([dataText], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `al-salamat-data-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                addResult('export-results', '✅ تم تحميل ملف البيانات', 'success');
            } else {
                addResult('export-results', '❌ لا توجد بيانات للتحميل. قم بالتصدير أولاً', 'error');
            }
        }

        async function resetToDefaultData() {
            const confirmMessage = 'هل أنت متأكد من إعادة تعيين جميع البيانات إلى القيم الافتراضية؟';
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            try {
                addResult('reset-results', 'بدء إعادة تعيين البيانات...', 'info');
                
                const updates = {};
                Object.keys(DEFAULT_WEBSITE_DATA).forEach(section => {
                    updates[section] = {
                        ...DEFAULT_WEBSITE_DATA[section],
                        updatedAt: new Date().toISOString(),
                        resetAt: new Date().toISOString()
                    };
                });
                
                await database.ref().update(updates);
                
                addResult('reset-results', '✅ تم إعادة تعيين جميع البيانات بنجاح', 'success');
                addResult('reset-results', '🔍 تحقق من الصفحة الرئيسية لرؤية التحديثات', 'warning');
                
            } catch (error) {
                addResult('reset-results', `❌ خطأ في إعادة التعيين: ${error.message}`, 'error');
            }
        }

        async function checkCurrentData() {
            try {
                addResult('check-results', 'فحص البيانات الحالية...', 'info');
                
                const sections = ['companyInfo', 'aboutSection', 'contactSection', 'siteSettings'];
                let checkHTML = '<div class="section-preview"><h4>📊 البيانات الحالية</h4>';
                
                for (const section of sections) {
                    const snapshot = await database.ref(section).once('value');
                    const data = snapshot.val();
                    
                    checkHTML += `<div class="data-item">`;
                    checkHTML += `<strong>${getSectionDisplayName(section)}:</strong><br>`;
                    
                    if (data) {
                        Object.entries(data).forEach(([key, value]) => {
                            if (key !== 'updatedAt' && key !== 'version' && key !== 'resetAt' && key !== 'updatedBy') {
                                const displayValue = typeof value === 'string' && value.length > 100 
                                    ? value.substring(0, 100) + '...' 
                                    : value;
                                checkHTML += `&nbsp;&nbsp;• ${getFieldDisplayName(key)}: ${displayValue}<br>`;
                            }
                        });
                        
                        if (data.updatedAt) {
                            checkHTML += `&nbsp;&nbsp;• آخر تحديث: ${new Date(data.updatedAt).toLocaleString()}<br>`;
                        }
                    } else {
                        checkHTML += `&nbsp;&nbsp;❌ لا توجد بيانات<br>`;
                    }
                    
                    checkHTML += `</div>`;
                }
                
                checkHTML += '</div>';
                document.getElementById('check-results').innerHTML = checkHTML;
                
                addResult('check-results', '✅ تم فحص البيانات بنجاح', 'success');
                
            } catch (error) {
                addResult('check-results', `❌ خطأ في الفحص: ${error.message}`, 'error');
            }
        }

        async function compareWithDefault() {
            try {
                addResult('compare-results', 'مقارنة البيانات...', 'info');
                
                const sections = ['companyInfo', 'aboutSection', 'contactSection', 'siteSettings'];
                let compareHTML = '<div class="section-preview"><h4>🔍 مقارنة البيانات</h4>';
                
                for (const section of sections) {
                    const snapshot = await database.ref(section).once('value');
                    const currentData = snapshot.val();
                    const defaultData = DEFAULT_WEBSITE_DATA[section];
                    
                    compareHTML += `<div class="data-item">`;
                    compareHTML += `<strong>${getSectionDisplayName(section)}:</strong><br>`;
                    
                    if (currentData) {
                        Object.keys(defaultData).forEach(key => {
                            if (key !== 'updatedAt' && key !== 'version') {
                                const current = currentData[key];
                                const defaultVal = defaultData[key];
                                const isMatch = current === defaultVal;
                                
                                compareHTML += `&nbsp;&nbsp;${isMatch ? '✅' : '❌'} ${getFieldDisplayName(key)}: ${isMatch ? 'متطابق' : 'مختلف'}<br>`;
                            }
                        });
                    } else {
                        compareHTML += `&nbsp;&nbsp;❌ لا توجد بيانات حالية<br>`;
                    }
                    
                    compareHTML += `</div>`;
                }
                
                compareHTML += '</div>';
                document.getElementById('compare-results').innerHTML = compareHTML;
                
                addResult('compare-results', '✅ تم إنجاز المقارنة', 'success');
                
            } catch (error) {
                addResult('compare-results', `❌ خطأ في المقارنة: ${error.message}`, 'error');
            }
        }

        function getSectionDisplayName(sectionName) {
            const names = {
                'companyInfo': 'معلومات الشركة',
                'aboutSection': 'قسم من نحن',
                'contactSection': 'قسم اتصل بنا',
                'siteSettings': 'إعدادات الموقع'
            };
            return names[sectionName] || sectionName;
        }

        function getFieldDisplayName(fieldName) {
            const names = {
                'title': 'العنوان',
                'subtitle': 'العنوان الفرعي',
                'description': 'الوصف',
                'infoTitle': 'عنوان المعلومات',
                'address': 'العنوان',
                'hours': 'ساعات العمل',
                'maintenance': 'وضع الصيانة',
                'contactEmail': 'البريد الإلكتروني',
                'contactPhone': 'رقم الهاتف'
            };
            return names[fieldName] || fieldName;
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(initializeSystem, 1000);
        });
    </script>
</body>
</html>
