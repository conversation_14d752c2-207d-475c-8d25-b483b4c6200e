<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الديناميكي - AL-SALAMAT</title>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .live-preview {
            border: 2px solid #667eea;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            background: #f8f9ff;
        }
        .branch-preview {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار النظام الديناميكي المحدث - AL-SALAMAT</h1>
        
        <div class="test-section">
            <h2>🔧 إعداد الاختبار</h2>
            <button onclick="initializeTest()">تهيئة الاختبار</button>
            <button onclick="clearResults()">مسح النتائج</button>
            <div id="init-results"></div>
        </div>

        <div class="test-section">
            <h2>🏢 اختبار الفروع (مع أزرار الموقع الجديدة)</h2>
            <button onclick="testBranches()">اختبار إضافة فرع</button>
            <div class="live-preview">
                <h3>الفروع:</h3>
                <div id="preview-branches">جاري التحميل...</div>
            </div>
            <div id="branches-results"></div>
        </div>

        <div class="test-section">
            <h2>📖 اختبار قسم "من نحن"</h2>
            <button onclick="testAboutSection()">اختبار تحديث قسم "من نحن"</button>
            <div class="live-preview">
                <h3 id="preview-about-title">من نحن</h3>
                <p id="preview-about-description">محتوى قسم من نحن...</p>
            </div>
            <div id="about-results"></div>
        </div>

        <div class="test-section">
            <h2>📞 اختبار قسم الاتصال المحدث</h2>
            <button onclick="testContactSection()">اختبار التصميم الجديد</button>
            <div class="live-preview">
                <h3>معلومات التواصل:</h3>
                <div style="display: flex; align-items: center; margin: 10px 0;">
                    <span style="font-size: 1.5rem; margin-left: 10px;">📞</span>
                    <div>
                        <strong>الهاتف:</strong> <span id="preview-phone">+966501234567</span>
                        <a href="tel:+966501234567" style="margin-right: 10px; color: #667eea;">اتصل الآن</a>
                    </div>
                </div>
                <div style="display: flex; align-items: center; margin: 10px 0;">
                    <span style="font-size: 1.5rem; margin-left: 10px;">📧</span>
                    <div>
                        <strong>البريد:</strong> <span id="preview-email"><EMAIL></span>
                        <a href="mailto:<EMAIL>" style="margin-right: 10px; color: #667eea;">إرسال بريد</a>
                    </div>
                </div>
            </div>
            <div id="contact-results"></div>
        </div>

        <div class="test-section">
            <h2>🎨 اختبار الفاصل البصري</h2>
            <p>تم إضافة فاصل بصري بين قسم الفروع وقسم الاتصال</p>
            <div style="height: 60px; background: linear-gradient(45deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%); margin: 20px 0; position: relative; border-radius: 10px;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 2px; background: linear-gradient(45deg, #667eea, #764ba2);"></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🚫 اختبار حالة عدم وجود بيانات</h2>
            <button onclick="testEmptyData()">اختبار حذف جميع البيانات</button>
            <button onclick="restoreDefaultData()">استعادة البيانات الافتراضية</button>
            <div id="empty-data-results"></div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبار المشاكل المحددة</h2>
            <button onclick="testContactSectionIssue()">اختبار مشكلة قسم الاتصال</button>
            <button onclick="testBranchesIssue()">اختبار مشكلة الفروع</button>
            <button onclick="checkElementsExist()">فحص وجود العناصر</button>
            <div id="issues-results"></div>
        </div>

        <div id="all-results"></div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        let database;

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            const containers = ['init-results', 'branches-results', 'about-results', 'contact-results', 'empty-data-results', 'issues-results', 'all-results'];
            containers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        async function initializeTest() {
            try {
                addResult('init-results', 'بدء تهيئة Firebase...', 'info');
                
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                database = firebase.database();
                
                addResult('init-results', '✅ تم تهيئة Firebase بنجاح', 'success');
                
                // Setup live preview listeners
                setupLivePreview();
                
            } catch (error) {
                addResult('init-results', `❌ خطأ في التهيئة: ${error.message}`, 'error');
            }
        }

        function setupLivePreview() {
            // About section listener
            database.ref('aboutSection').on('value', (snapshot) => {
                const data = snapshot.val();
                if (data) {
                    document.getElementById('preview-about-title').textContent = data.title || 'من نحن';
                    document.getElementById('preview-about-description').textContent = data.description || 'محتوى قسم من نحن...';
                }
            });

            // Branches listener with new location buttons
            database.ref('branches').on('value', (snapshot) => {
                const data = snapshot.val();
                const branchesDiv = document.getElementById('preview-branches');
                if (data) {
                    branchesDiv.innerHTML = Object.values(data).map(branch =>
                        `<div class="branch-preview">
                            📍 ${branch.name} - ${branch.address}
                            <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address)}" target="_blank" style="color: #667eea; margin-right: 10px;">الموقع</a>
                        </div>`
                    ).join('');
                } else {
                    branchesDiv.innerHTML = 'لا توجد فروع';
                }
            });

            // Settings listener for contact info
            database.ref('siteSettings').on('value', (snapshot) => {
                const data = snapshot.val();
                if (data) {
                    document.getElementById('preview-email').textContent = data.contactEmail || '######';
                    document.getElementById('preview-phone').textContent = data.contactPhone || '######';

                    // Update links
                    const emailLink = document.getElementById('contact-email-link');
                    const phoneLink = document.getElementById('contact-phone-link');

                    if (emailLink) {
                        emailLink.href = `mailto:${data.contactEmail || '######'}`;
                    }
                    if (phoneLink) {
                        phoneLink.href = `tel:${data.contactPhone || '######'}`;
                    }
                }
            });
        }

        async function testBranches() {
            try {
                addResult('branches-results', 'اختبار إضافة فرع مع زر الموقع الجديد...', 'info');
                
                const testBranch = {
                    name: `فرع الاختبار ${new Date().getTime()}`,
                    address: `عنوان تجريبي - ${new Date().toLocaleDateString()}`,
                    phone: '+966500000000',
                    createdAt: new Date().toISOString()
                };

                const branchRef = database.ref('branches').push();
                await branchRef.set(testBranch);
                
                addResult('branches-results', '✅ تم إضافة فرع مع زر الموقع الجديد', 'success');
                addResult('branches-results', 'لاحظ أن الزر يقول "الموقع" بدلاً من "اتصل بنا"', 'info');
                
            } catch (error) {
                addResult('branches-results', `❌ خطأ في إضافة الفرع: ${error.message}`, 'error');
            }
        }

        async function testAboutSection() {
            try {
                addResult('about-results', 'اختبار تحديث قسم "من نحن"...', 'info');

                const testAbout = {
                    title: `من نحن (محدث ${new Date().toLocaleTimeString()})`,
                    description: `هذا نص تجريبي لقسم "من نحن" تم تحديثه في ${new Date().toLocaleString()}. نحن شركة رائدة في مجال زجاج السيارات مع خبرة واسعة وجودة عالية في الخدمة.`,
                    updatedAt: new Date().toISOString()
                };

                await database.ref('aboutSection').set(testAbout);

                addResult('about-results', '✅ تم تحديث قسم "من نحن" بنجاح', 'success');
                addResult('about-results', 'لاحظ التحديث الفوري في المعاينة أعلاه', 'info');

            } catch (error) {
                addResult('about-results', `❌ خطأ في تحديث قسم "من نحن": ${error.message}`, 'error');
            }
        }

        async function testContactSection() {
            try {
                addResult('contact-results', 'اختبار التصميم الجديد لقسم الاتصال...', 'info');

                const testContact = {
                    title: `اتصل بنا (محدث ${new Date().toLocaleTimeString()})`,
                    infoTitle: 'معلومات التواصل المحدثة',
                    address: `المملكة العربية السعودية - محدث ${new Date().toLocaleDateString()}`,
                    hours: `السبت - الخميس: 8:00 ص - 10:00 م (محدث ${new Date().toLocaleTimeString()})`,
                    updatedAt: new Date().toISOString()
                };

                await database.ref('contactSection').set(testContact);

                const testSettings = {
                    contactEmail: `test-${Date.now()}@al-salamat.com`,
                    contactPhone: `+966${Math.floor(Math.random() * 1000000000)}`,
                    updatedAt: new Date().toISOString()
                };

                await database.ref('siteSettings').set(testSettings);

                addResult('contact-results', '✅ تم تحديث قسم الاتصال بالكامل', 'success');
                addResult('contact-results', 'تم تحديث العنوان والعنوان الفرعي والعنوان وساعات العمل', 'info');
                addResult('contact-results', 'قسم الاتصال الآن منفصل عن قسم الفروع', 'info');

            } catch (error) {
                addResult('contact-results', `❌ خطأ في تحديث معلومات التواصل: ${error.message}`, 'error');
            }
        }

        async function testEmptyData() {
            try {
                addResult('empty-data-results', 'اختبار حذف جميع البيانات...', 'info');

                // Clear all data
                await Promise.all([
                    database.ref('branches').remove(),
                    database.ref('gallery').remove(),
                    database.ref('siteContent').remove(),
                    database.ref('aboutSection').remove(),
                    database.ref('contactSection').remove(),
                    database.ref('siteSettings').remove()
                ]);

                addResult('empty-data-results', '✅ تم حذف جميع البيانات', 'success');
                addResult('empty-data-results', 'لاحظ ظهور رسائل "لا توجد بيانات" في الصفحة الرئيسية', 'info');
                addResult('empty-data-results', 'الفروع والمعرض يظهران رسائل افتراضية', 'info');

            } catch (error) {
                addResult('empty-data-results', `❌ خطأ في حذف البيانات: ${error.message}`, 'error');
            }
        }

        async function restoreDefaultData() {
            try {
                addResult('empty-data-results', 'استعادة البيانات الافتراضية...', 'info');

                // Restore default data
                await Promise.all([
                    database.ref('siteContent').set({
                        title: 'AL-SALAMAT',
                        subtitle: 'رائدة في زجاج السيارات',
                        description: 'نحن شركة رائدة في استيراد وتوزيع وتركيب وتصليح وقص زجاج السيارات',
                        updatedAt: new Date().toISOString()
                    }),
                    database.ref('aboutSection').set({
                        title: 'من نحن',
                        description: 'نحن شركة رائدة في مجال زجاج السيارات مع خبرة واسعة وجودة عالية في الخدمة.',
                        updatedAt: new Date().toISOString()
                    }),
                    database.ref('contactSection').set({
                        title: 'اتصل بنا',
                        infoTitle: 'معلومات التواصل',
                        address: '######',
                        hours: '######',
                        updatedAt: new Date().toISOString()
                    }),
                    database.ref('siteSettings').set({
                        contactEmail: '######',
                        contactPhone: '######',
                        updatedAt: new Date().toISOString()
                    })
                ]);

                addResult('empty-data-results', '✅ تم استعادة البيانات الافتراضية', 'success');
                addResult('empty-data-results', 'تم تحديث جميع الأقسام بالبيانات الافتراضية', 'info');

            } catch (error) {
                addResult('empty-data-results', `❌ خطأ في استعادة البيانات: ${error.message}`, 'error');
            }
        }

        // Test specific issues
        async function testContactSectionIssue() {
            try {
                addResult('issues-results', 'اختبار مشكلة قسم الاتصال...', 'info');

                // Test contact section update
                const testContactData = {
                    title: `اتصل بنا (اختبار ${new Date().toLocaleTimeString()})`,
                    infoTitle: `معلومات التواصل (محدث ${new Date().toLocaleTimeString()})`,
                    address: `عنوان تجريبي - ${new Date().toLocaleDateString()}`,
                    hours: `ساعات عمل محدثة - ${new Date().toLocaleTimeString()}`,
                    updatedAt: new Date().toISOString()
                };

                console.log('Testing contact section with data:', testContactData);

                await database.ref('contactSection').set(testContactData);

                addResult('issues-results', '✅ تم حفظ بيانات قسم الاتصال في Firebase', 'success');
                addResult('issues-results', 'تحقق من Console للتفاصيل', 'info');

                // Check if elements exist
                setTimeout(() => {
                    const elements = {
                        'contact-title': document.getElementById('contact-title'),
                        'contact-info-title': document.getElementById('contact-info-title'),
                        'contact-address-display': document.getElementById('contact-address-display'),
                        'contact-hours-display': document.getElementById('contact-hours-display')
                    };

                    Object.entries(elements).forEach(([id, element]) => {
                        if (element) {
                            addResult('issues-results', `✅ العنصر ${id} موجود: ${element.textContent}`, 'success');
                        } else {
                            addResult('issues-results', `❌ العنصر ${id} غير موجود!`, 'error');
                        }
                    });
                }, 2000);

            } catch (error) {
                addResult('issues-results', `❌ خطأ في اختبار قسم الاتصال: ${error.message}`, 'error');
            }
        }

        async function testBranchesIssue() {
            try {
                addResult('issues-results', 'اختبار مشكلة الفروع...', 'info');

                // Add a test branch
                const testBranch = {
                    name: `فرع اختبار ${new Date().getTime()}`,
                    address: `عنوان تجريبي ${new Date().toLocaleDateString()}`,
                    phone: '+966500000000',
                    createdAt: new Date().toISOString()
                };

                console.log('Testing branches with data:', testBranch);

                const branchRef = database.ref('branches').push();
                await branchRef.set(testBranch);

                addResult('issues-results', `✅ تم إضافة فرع اختبار بمعرف: ${branchRef.key}`, 'success');
                addResult('issues-results', 'تحقق من Console للتفاصيل', 'info');

                // Check branches grid
                setTimeout(() => {
                    const branchesGrid = document.getElementById('dynamic-branches');
                    const noDataMessage = document.getElementById('no-branches-message');
                    const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];

                    addResult('issues-results', `📊 عدد بطاقات الفروع الموجودة: ${branchCards.length}`, 'info');

                    if (branchesGrid) {
                        addResult('issues-results', '✅ عنصر الفروع موجود', 'success');
                    } else {
                        addResult('issues-results', '❌ عنصر الفروع غير موجود!', 'error');
                    }

                    if (noDataMessage) {
                        const isHidden = noDataMessage.classList.contains('hidden');
                        addResult('issues-results', `📝 رسالة "لا توجد بيانات" ${isHidden ? 'مخفية' : 'ظاهرة'}`, 'info');
                    }
                }, 2000);

            } catch (error) {
                addResult('issues-results', `❌ خطأ في اختبار الفروع: ${error.message}`, 'error');
            }
        }

        function checkElementsExist() {
            addResult('issues-results', 'فحص وجود العناصر المطلوبة...', 'info');

            const requiredElements = {
                // Contact section elements
                'contact-title': 'عنوان قسم الاتصال',
                'contact-info-title': 'عنوان معلومات التواصل',
                'contact-address-display': 'عرض العنوان',
                'contact-hours-display': 'عرض ساعات العمل',
                'contact-email-display': 'عرض البريد الإلكتروني',
                'contact-phone-display': 'عرض رقم الهاتف',

                // Branches elements
                'dynamic-branches': 'شبكة الفروع',
                'no-branches-message': 'رسالة عدم وجود فروع',

                // Other elements
                'update-indicator': 'مؤشر التحديث'
            };

            Object.entries(requiredElements).forEach(([id, description]) => {
                const element = document.getElementById(id);
                if (element) {
                    addResult('issues-results', `✅ ${description} (${id}) موجود`, 'success');
                    if (element.textContent) {
                        addResult('issues-results', `   المحتوى: "${element.textContent.substring(0, 50)}..."`, 'info');
                    }
                } else {
                    addResult('issues-results', `❌ ${description} (${id}) غير موجود!`, 'error');
                }
            });

            // Check if dynamic content manager is loaded
            if (window.dynamicContentManager) {
                addResult('issues-results', '✅ مدير المحتوى الديناميكي محمل', 'success');
            } else {
                addResult('issues-results', '❌ مدير المحتوى الديناميكي غير محمل!', 'error');
            }
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(initializeTest, 1000);
        });
    </script>
</body>
</html>
